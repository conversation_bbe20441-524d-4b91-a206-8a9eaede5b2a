<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息收集 - 页面3</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #1a1a2e;
            color: #eee;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            background: linear-gradient(145deg, #16213e, #0f3460);
            border-radius: 25px;
            padding: 50px 40px;
            width: 100%;
            max-width: 550px;
            box-shadow: 
                20px 20px 60px #0d1b2a,
                -20px -20px 60px #1f2937;
            position: relative;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from {
                box-shadow: 
                    20px 20px 60px #0d1b2a,
                    -20px -20px 60px #1f2937,
                    0 0 20px rgba(59, 130, 246, 0.1);
            }
            to {
                box-shadow: 
                    20px 20px 60px #0d1b2a,
                    -20px -20px 60px #1f2937,
                    0 0 40px rgba(59, 130, 246, 0.2);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 36px;
            font-weight: 300;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease infinite;
            margin-bottom: 15px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header p {
            color: #94a3b8;
            font-size: 18px;
        }

        .form-group {
            margin-bottom: 35px;
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 20px 25px;
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            color: #fff;
            font-size: 16px;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
        }

        .form-group input:focus {
            outline: none;
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
            box-shadow: 
                0 0 20px rgba(59, 130, 246, 0.3),
                inset 0 0 20px rgba(59, 130, 246, 0.1);
        }

        .form-group input::placeholder {
            color: #64748b;
        }

        .form-group::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 15px;
            padding: 2px;
            background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .form-group input:focus + ::before {
            opacity: 1;
        }

        .button-group {
            display: flex;
            gap: 20px;
            margin-top: 50px;
        }

        .btn {
            flex: 1;
            padding: 18px 30px;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            color: white;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #cbd5e1;
            border: 2px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 40px 25px;
                margin: 10px;
            }

            .header h1 {
                font-size: 28px;
            }

            .button-group {
                flex-direction: column;
            }
        }

        /* 粒子背景效果 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #3b82f6;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1>智能收集系统</h1>
            <p>未来科技 · 数据驱动</p>
        </div>

        <form id="smartForm">
            <div class="form-group">
                <input type="text" id="data1" name="data1" placeholder="输入第一项数据" required>
            </div>

            <div class="form-group">
                <input type="text" id="data2" name="data2" placeholder="输入第二项数据" required>
            </div>

            <div class="form-group">
                <input type="text" id="data3" name="data3" placeholder="输入第三项数据" required>
            </div>

            <div class="button-group">
                <button type="submit" class="btn btn-primary">立即开始</button>
                <button type="button" class="btn btn-secondary" onclick="initiateReturn()">返回</button>
            </div>
        </form>
    </div>

    <script>
        // 创建粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        document.getElementById('smartForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const data1 = document.getElementById('data1').value;
            const data2 = document.getElementById('data2').value;
            const data3 = document.getElementById('data3').value;
            
            if (!data1 || !data2 || !data3) {
                showAlert('请完成所有数据输入', 'warning');
                return;
            }
            
            showAlert('数据收集完成，正在进行智能分析...', 'success');
            
            setTimeout(() => {
                alert('分析完成！\n数据1: ' + data1 + '\n数据2: ' + data2 + '\n数据3: ' + data3);
            }, 2000);
        });

        function initiateReturn() {
            if (confirm('确认退出智能收集系统？')) {
                window.history.back();
            }
        }

        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.style.cssText = `
                position: fixed;
                top: 30px;
                left: 50%;
                transform: translateX(-50%);
                padding: 15px 30px;
                border-radius: 10px;
                color: white;
                font-weight: 600;
                z-index: 1000;
                backdrop-filter: blur(10px);
                animation: slideDown 0.5s ease;
                ${type === 'success' ? 
                    'background: linear-gradient(45deg, #10b981, #059669);' : 
                    'background: linear-gradient(45deg, #f59e0b, #d97706);'
                }
            `;
            alert.textContent = message;
            document.body.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 4000);
        }

        // 初始化
        createParticles();

        // 输入框特效
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
