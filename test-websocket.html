<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coinbase WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .price-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .price-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .price-item.up { border-left-color: #28a745; }
        .price-item.down { border-left-color: #dc3545; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Coinbase WebSocket 连接测试</h1>
        
        <div id="status" class="status info">准备连接...</div>
        
        <div>
            <button id="connectBtn" onclick="connectWebSocket()">连接 WebSocket</button>
            <button id="disconnectBtn" onclick="disconnectWebSocket()" disabled>断开连接</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="price-display" id="priceDisplay"></div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let websocket = null;
        let isConnected = false;
        let priceData = {};

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }

        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function connectWebSocket() {
            try {
                log('🔗 开始连接到 Coinbase WebSocket...');
                updateStatus('正在连接...', 'info');
                
                const wsUrl = 'wss://ws-feed.exchange.coinbase.com';
                websocket = new WebSocket(wsUrl);
                
                document.getElementById('connectBtn').disabled = true;

                websocket.onopen = function() {
                    log('✅ WebSocket 连接成功！');
                    updateStatus('已连接', 'success');
                    isConnected = true;
                    
                    document.getElementById('disconnectBtn').disabled = false;
                    
                    // 发送订阅消息
                    const subscribeMessage = {
                        type: 'subscribe',
                        product_ids: ['BTC-USD', 'ETH-USD', 'ADA-USD'],
                        channels: [
                            {
                                name: 'ticker',
                                product_ids: ['BTC-USD', 'ETH-USD', 'ADA-USD']
                            }
                        ]
                    };
                    
                    log('📡 发送订阅消息: ' + JSON.stringify(subscribeMessage));
                    websocket.send(JSON.stringify(subscribeMessage));
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        
                        if (data.type === 'subscriptions') {
                            log('✅ 订阅确认: ' + JSON.stringify(data));
                        } else if (data.type === 'ticker') {
                            handleTickerUpdate(data);
                        } else if (data.type === 'error') {
                            log('❌ WebSocket 错误: ' + JSON.stringify(data));
                        } else {
                            log('📨 收到消息: ' + data.type);
                        }
                    } catch (error) {
                        log('❌ 消息解析错误: ' + error.message);
                    }
                };

                websocket.onerror = function(error) {
                    log('❌ WebSocket 错误: ' + error);
                    updateStatus('连接错误', 'error');
                };

                websocket.onclose = function(event) {
                    log(`🔌 WebSocket 连接关闭 (代码: ${event.code}, 原因: ${event.reason})`);
                    updateStatus('连接已关闭', 'error');
                    isConnected = false;
                    
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                };

            } catch (error) {
                log('❌ 连接失败: ' + error.message);
                updateStatus('连接失败', 'error');
                document.getElementById('connectBtn').disabled = false;
            }
        }

        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        function handleTickerUpdate(ticker) {
            const symbol = ticker.product_id.replace('-USD', '');
            const price = parseFloat(ticker.price);
            const open24h = parseFloat(ticker.open_24h);
            const change24h = open24h ? ((price - open24h) / open24h) * 100 : 0;

            priceData[symbol] = {
                price: price,
                change24h: change24h,
                time: ticker.time
            };

            log(`💰 ${symbol}: $${price.toFixed(2)} (${change24h > 0 ? '+' : ''}${change24h.toFixed(2)}%)`);
            
            updatePriceDisplay();
        }

        function updatePriceDisplay() {
            const priceDisplay = document.getElementById('priceDisplay');
            priceDisplay.innerHTML = '';

            Object.entries(priceData).forEach(([symbol, data]) => {
                const priceItem = document.createElement('div');
                priceItem.className = `price-item ${data.change24h > 0 ? 'up' : 'down'}`;
                priceItem.innerHTML = `
                    <div style="font-weight: bold; font-size: 18px;">${symbol}</div>
                    <div style="font-size: 16px;">$${data.price.toFixed(2)}</div>
                    <div style="font-size: 14px; color: ${data.change24h > 0 ? '#28a745' : '#dc3545'};">
                        ${data.change24h > 0 ? '+' : ''}${data.change24h.toFixed(2)}%
                    </div>
                    <div style="font-size: 12px; color: #6c757d;">
                        ${new Date(data.time).toLocaleTimeString()}
                    </div>
                `;
                priceDisplay.appendChild(priceItem);
            });
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // 页面加载完成后显示说明
        window.onload = function() {
            log('📋 Coinbase WebSocket 测试页面已加载');
            log('📋 点击"连接 WebSocket"按钮开始测试');
            log('📋 这个页面将测试与 Coinbase 的 WebSocket 连接');
        };
    </script>
</body>
</html>
