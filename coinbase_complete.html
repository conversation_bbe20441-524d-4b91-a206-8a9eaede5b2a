<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore · Coinbase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            color: #1a1a1a;
            font-size: 14px;
            line-height: 1.4;
            overflow-x: hidden;
        }

        .container {
            max-width: 412px;
            margin: 0 auto;
            background: #ffffff;
            min-height: 100vh;
            position: relative;
        }

        /* Header */
        .header {
            padding: 12px 16px;
            background: #ffffff;
            border-bottom: 1px solid #f3f4f6;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 500;
            color: #1a1a1a;
        }

        /* Main Content */
        .main-content {
            padding: 16px;
            padding-bottom: 100px; /* Space for bottom nav */
        }

        .section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #1a1a1a;
        }

        .section-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 16px;
        }

        /* Earning Assets */
        .earning-item, .price-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            font-size: 14px;
            border-bottom: 1px solid #f9fafb;
        }

        .earning-item:last-child, .price-item:last-child {
            border-bottom: none;
        }

        .item-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .coin-name {
            color: #1a1a1a;
            font-weight: 400;
        }

        .coin-symbol {
            color: #6b7280;
        }

        .apy-info {
            color: #6b7280;
        }

        .item-right {
            text-align: right;
        }

        .apy-value {
            color: #16a34a;
            font-weight: 400;
        }

        .price-value {
            color: #1a1a1a;
            font-weight: 400;
            margin-bottom: 2px;
        }

        .price-change {
            font-size: 14px;
            font-weight: 400;
        }

        .positive { color: #16a34a; }
        .negative { color: #dc2626; }
        .neutral { color: #6b7280; }

        .see-all {
            text-align: center;
            padding: 16px 0;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 412px;
            background: #ffffff;
            border-top: 1px solid #e5e7eb;
            padding: 8px 0;
            z-index: 1000;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-item.active {
            color: #0052ff;
        }

        .nav-item svg {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
        }

        .nav-item span {
            font-size: 10px;
            font-weight: 500;
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 80px;
            right: 16px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #0052ff, #0041cc);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 82, 255, 0.3);
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 999;
        }

        .fab:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 82, 255, 0.4);
        }

        .fab svg {
            width: 24px;
            height: 24px;
            fill: white;
        }

        /* Price Update Animation */
        .price-update {
            transition: all 0.3s ease;
        }

        .price-flash-up {
            background-color: #dcfce7 !important;
            border-radius: 4px;
            padding: 2px 4px;
        }

        .price-flash-down {
            background-color: #fef2f2 !important;
            border-radius: 4px;
            padding: 2px 4px;
        }

        /* Buy buttons */
        .buy-btn {
            background: linear-gradient(135deg, #0052ff, #0041cc);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .buy-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 82, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Explore</h1>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Earning Assets Section -->
            <div class="section">
                <h2 class="section-title">Earning assets</h2>
                <p class="section-subtitle">Earn rewards by buying and staking eligible assets</p>
                
                <div class="earning-item">
                    <div class="item-left">
                        <span class="coin-name">Cosmos</span>
                        <span class="coin-symbol">ATOM</span>
                    </div>
                    <div class="item-right">
                        <span class="apy-value">+0.30% APY</span>
                    </div>
                </div>

                <div class="earning-item">
                    <div class="item-left">
                        <span class="coin-name">Polkadot</span>
                        <span class="coin-symbol">DOT</span>
                    </div>
                    <div class="item-right">
                        <span class="apy-value">+0.30% APY</span>
                    </div>
                </div>

                <div class="earning-item">
                    <div class="item-left">
                        <span class="coin-name">Solana</span>
                        <span class="coin-symbol">SOL</span>
                    </div>
                    <div class="item-right">
                        <span class="apy-value">+0.30% APY</span>
                    </div>
                </div>

                <div class="earning-item">
                    <div class="item-left">
                        <span class="coin-name">Avalanche</span>
                        <span class="coin-symbol">AVAX</span>
                    </div>
                    <div class="item-right">
                        <span class="apy-value">+0.30% APY</span>
                    </div>
                </div>

                <div class="earning-item">
                    <div class="item-left">
                        <span class="coin-name">Tezos</span>
                        <span class="coin-symbol">XTZ</span>
                    </div>
                    <div class="item-right">
                        <span class="apy-value">+0.30% APY</span>
                    </div>
                </div>

                <div class="see-all">See all</div>
            </div>

            <!-- Prices Section -->
            <div class="section">
                <h2 class="section-title">Prices</h2>
                
                <div class="price-item" data-symbol="BTC">
                    <div class="item-left">
                        <span class="coin-name">Bitcoin</span>
                        <span class="coin-symbol">BTC</span>
                        <button class="buy-btn" onclick="showBuyModal('BTC')">Buy</button>
                    </div>
                    <div class="item-right">
                        <div class="price-value" data-price="idr">IDR 660,034,517.70</div>
                        <div class="price-change negative" data-change>-0.26%</div>
                    </div>
                </div>

                <div class="price-item" data-symbol="ETH">
                    <div class="item-left">
                        <span class="coin-name">Ethereum</span>
                        <span class="coin-symbol">ETH</span>
                        <span class="apy-info">· 1.93% APY</span>
                        <button class="buy-btn" onclick="showBuyModal('ETH')">Buy</button>
                    </div>
                    <div class="item-right">
                        <div class="price-value" data-price="idr">IDR 39,474,328.53</div>
                        <div class="price-change negative" data-change>-2.33%</div>
                    </div>
                </div>

                <div class="price-item" data-symbol="XRP">
                    <div class="item-left">
                        <span class="coin-name">XRP</span>
                        <span class="coin-symbol">XRP</span>
                        <button class="buy-btn" onclick="showBuyModal('XRP')">Buy</button>
                    </div>
                    <div class="item-right">
                        <div class="price-value" data-price="idr">IDR 10,710.28</div>
                        <div class="price-change negative" data-change>-4.01%</div>
                    </div>
                </div>

                <div class="price-item" data-symbol="USDT">
                    <div class="item-left">
                        <span class="coin-name">Tether</span>
                        <span class="coin-symbol">USDT</span>
                        <button class="buy-btn" onclick="showBuyModal('USDT')">Buy</button>
                    </div>
                    <div class="item-right">
                        <div class="price-value" data-price="idr">IDR 15,300.00</div>
                        <div class="price-change neutral" data-change>-0.00%</div>
                    </div>
                </div>

                <div class="price-item" data-symbol="BNB">
                    <div class="item-left">
                        <span class="coin-name">BNB</span>
                        <span class="coin-symbol">BNB</span>
                        <button class="buy-btn" onclick="showBuyModal('BNB')">Buy</button>
                    </div>
                    <div class="item-right">
                        <div class="price-value" data-price="idr">IDR 9,256,500.00</div>
                        <div class="price-change negative" data-change>-2.16%</div>
                    </div>
                </div>

                <div class="see-all">See all</div>
            </div>
        </div>

        <!-- Floating Action Button -->
        <div class="fab" onclick="showTransferSheet()">
            <svg viewBox="0 0 24 24">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-items">
                <div class="nav-item">
                    <svg fill="currentColor" viewBox="0 0 24 24">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                    <span>Home</span>
                </div>
                <div class="nav-item active">
                    <svg fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    <span>Explore</span>
                </div>
                <div class="nav-item">
                    <svg fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 11H7v9a2 2 0 002 2h8a2 2 0 002-2V9h2l-1-1h-4V6a2 2 0 00-2-2h-4a2 2 0 00-2 2v2H4l-1 1h2v2h4zm0-5a1 1 0 011-1h4a1 1 0 011 1v1H9V6z"/>
                    </svg>
                    <span>Portfolio</span>
                </div>
                <div class="nav-item">
                    <svg fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                    <span>Profile</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Transfer Bottom Sheet -->
    <div id="transferSheet" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.5); z-index:10000;">
        <div id="transferContent" style="position:absolute; bottom:-100%; left:0; width:100%; max-width:412px; left:50%; transform:translateX(-50%); max-height:80%; background-color:white; border-radius:20px 20px 0 0; transition:bottom 0.3s cubic-bezier(0.4, 0.0, 0.2, 1); box-shadow:0 -8px 40px rgba(0,0,0,0.15);">
            <!-- 拖拽指示器 -->
            <div style="width:36px; height:5px; background-color:#d1d5db; border-radius:3px; margin:12px auto 8px; cursor:pointer;" onclick="hideTransferSheet()"></div>

            <!-- Transfer Options -->
            <div style="padding:24px 24px 32px 24px;">
                <div style="display:flex; flex-direction:column; gap:20px; margin-bottom:24px;">
                    <!-- Buy Option -->
                    <div onclick="showBuyModal('general')" style="padding:16px; cursor:pointer; display:flex; align-items:center; border-radius:12px; transition:background-color 0.2s ease;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                        <div style="width:24px; height:24px; display:flex; align-items:center; justify-content:center; margin-right:16px;">
                            <svg width="20" height="20" fill="black" viewBox="0 0 24 24">
                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 style="margin:0 0 2px 0; font-size:16px; font-weight:600; color:#1f2937;">Buy</h3>
                            <p style="margin:0; font-size:13px; color:#6b7280; line-height:1.2;">Buy crypto with cash</p>
                        </div>
                    </div>

                    <!-- Sell Option -->
                    <div onclick="showBuyModal('sell')" style="padding:16px; cursor:pointer; display:flex; align-items:center; border-radius:12px; transition:background-color 0.2s ease;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                        <div style="width:24px; height:24px; display:flex; align-items:center; justify-content:center; margin-right:16px;">
                            <svg width="20" height="20" fill="black" viewBox="0 0 24 24">
                                <path d="M19 13H5v-2h14v2z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 style="margin:0 0 2px 0; font-size:16px; font-weight:600; color:#1f2937;">Sell</h3>
                            <p style="margin:0; font-size:13px; color:#6b7280; line-height:1.2;">Sell crypto for cash</p>
                        </div>
                    </div>

                    <!-- Convert Option -->
                    <div onclick="showBuyModal('convert')" style="padding:16px; cursor:pointer; display:flex; align-items:center; border-radius:12px; transition:background-color 0.2s ease;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                        <div style="width:24px; height:24px; display:flex; align-items:center; justify-content:center; margin-right:16px;">
                            <svg width="20" height="20" fill="black" viewBox="0 0 24 24">
                                <path d="M6.99 11L3 15l3.99 4v-3H14v-2H6.99v-3zM21 9l-3.99-4v3H10v2h7.01v3L21 9z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 style="margin:0 0 2px 0; font-size:16px; font-weight:600; color:#1f2937;">Convert</h3>
                            <p style="margin:0; font-size:13px; color:#6b7280; line-height:1.2;">Convert one crypto to another</p>
                        </div>
                    </div>

                    <!-- Send crypto Option -->
                    <div onclick="showBuyModal('send')" style="padding:16px; cursor:pointer; display:flex; align-items:center; border-radius:12px; transition:background-color 0.2s ease;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                        <div style="width:24px; height:24px; display:flex; align-items:center; justify-content:center; margin-right:16px;">
                            <svg width="20" height="20" fill="black" viewBox="0 0 24 24">
                                <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(-90 12 12)"/>
                            </svg>
                        </div>
                        <div>
                            <h3 style="margin:0 0 2px 0; font-size:16px; font-weight:600; color:#1f2937;">Send crypto</h3>
                            <p style="margin:0; font-size:13px; color:#6b7280; line-height:1.2;">To a crypto address, email, or phone number</p>
                        </div>
                    </div>

                    <!-- Receive crypto Option -->
                    <div onclick="showBuyModal('receive')" style="padding:16px; cursor:pointer; display:flex; align-items:center; border-radius:12px; transition:background-color 0.2s ease;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                        <div style="width:24px; height:24px; display:flex; align-items:center; justify-content:center; margin-right:16px;">
                            <svg width="20" height="20" fill="black" viewBox="0 0 24 24">
                                <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z" transform="rotate(90 12 12)"/>
                            </svg>
                        </div>
                        <div>
                            <h3 style="margin:0 0 2px 0; font-size:16px; font-weight:600; color:#1f2937;">Receive crypto</h3>
                            <p style="margin:0; font-size:13px; color:#6b7280; line-height:1.2;">From another crypto wallet</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Buys Not Supported Modal -->
    <div id="buysNotSupportedModal" style="
        display:none;
        position:fixed;
        top:0;
        left:0;
        width:100%;
        height:100%;
        background-color:rgba(0,0,0,0.5);
        z-index:20000;
        align-items:center;
        justify-content:center;
        opacity:0;
        transition:opacity 0.3s ease;
    ">
        <div style="
            background:white;
            border-radius:16px;
            padding:32px;
            max-width:400px;
            width:90%;
            text-align:center;
            box-shadow:0 20px 40px rgba(0,0,0,0.15);
            transform:scale(0.9);
            transition:transform 0.3s ease;
        " id="modalContent">
            <!-- Warning Icon -->
            <div style="margin-bottom:20px;">
                <div style="
                    width:60px;
                    height:60px;
                    margin:0 auto;
                    display:flex;
                    align-items:center;
                    justify-content:center;
                ">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L1 21h22L12 2z" fill="#f97316" stroke="#f97316" stroke-width="1"/>
                        <path d="M12 8v4" stroke="white" stroke-width="2" stroke-linecap="round"/>
                        <circle cx="12" cy="16" r="1" fill="white"/>
                    </svg>
                </div>
            </div>

            <!-- Title -->
            <h2 style="margin:0 0 16px 0; font-size:20px; font-weight:600; color:#1f2937; text-align:center;">Buys Not Supported</h2>

            <!-- Description -->
            <p style="margin:0 0 32px 0; font-size:14px; color:#6b7280; line-height:1.5; text-align:center;">
                Coinbase does not currently support buys in your country. Subscribe to our blog to be notified when we add support for your country!
            </p>

            <!-- Buttons -->
            <div style="display:flex; flex-direction:column; gap:12px;">
                <button onclick="window.open('https://blog.coinbase.com', '_blank')" style="
                    background:linear-gradient(135deg, #0052ff, #0041cc);
                    color:white;
                    border:none;
                    border-radius:12px;
                    padding:14px 24px;
                    font-size:16px;
                    font-weight:600;
                    cursor:pointer;
                    transition:all 0.2s ease;
                    box-shadow:0 4px 12px rgba(0, 82, 255, 0.3);
                ">Subscribe Now</button>

                <button onclick="hideBuyModal()" style="
                    background:#f3f4f6;
                    color:#6b7280;
                    border:none;
                    border-radius:12px;
                    padding:14px 24px;
                    font-size:16px;
                    font-weight:600;
                    cursor:pointer;
                    transition:all 0.2s ease;
                ">Go Back</button>
            </div>
        </div>
    </div>

    <script>
        // ===== 弹窗和交互功能 =====
        function showTransferSheet() {
            const sheet = document.getElementById('transferSheet');
            const content = document.getElementById('transferContent');

            sheet.style.display = 'flex';
            setTimeout(() => {
                content.style.bottom = '0';
            }, 10);
        }

        function hideTransferSheet() {
            const sheet = document.getElementById('transferSheet');
            const content = document.getElementById('transferContent');

            content.style.bottom = '-100%';
            setTimeout(() => {
                sheet.style.display = 'none';
            }, 300);
        }

        function showBuyModal(symbol) {
            hideTransferSheet();

            const modal = document.getElementById('buysNotSupportedModal');
            const modalContent = document.getElementById('modalContent');

            modal.style.display = 'flex';
            setTimeout(() => {
                modal.style.opacity = '1';
                modalContent.style.transform = 'scale(1)';
            }, 10);
        }

        function hideBuyModal() {
            const modal = document.getElementById('buysNotSupportedModal');
            const modalContent = document.getElementById('modalContent');

            modal.style.opacity = '0';
            modalContent.style.transform = 'scale(0.9)';
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 点击背景关闭弹窗
        document.getElementById('transferSheet').addEventListener('click', function(e) {
            if (e.target === this) {
                hideTransferSheet();
            }
        });

        document.getElementById('buysNotSupportedModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideBuyModal();
            }
        });

        // ===== Coinbase实时价格更新系统 =====
        class CoinbasePriceUpdater {
            constructor() {
                this.websocket = null;
                this.isConnected = false;
                this.priceData = {};
                this.updateInterval = null;

                // 币种配置
                this.cryptoConfig = {
                    'BTC': { name: 'Bitcoin', symbol: 'BTC-USD', basePrice: 43250, volatility: 0.02 },
                    'ETH': { name: 'Ethereum', symbol: 'ETH-USD', basePrice: 2580, volatility: 0.03 },
                    'XRP': { name: 'XRP', symbol: 'XRP-USD', basePrice: 0.70, volatility: 0.05 },
                    'USDT': { name: 'Tether', symbol: 'USDT-USD', basePrice: 1.00, volatility: 0.001 },
                    'BNB': { name: 'BNB', symbol: 'BNB-USD', basePrice: 605, volatility: 0.03 }
                };

                this.usdToIdrRate = 15300;
            }

            init() {
                console.log('🚀 启动Coinbase实时价格更新系统...');
                this.generateInitialPrices();
                this.connectWebSocket();
                this.startPriceUpdates();
            }

            generateInitialPrices() {
                Object.entries(this.cryptoConfig).forEach(([symbol, config]) => {
                    const change24h = (Math.random() - 0.5) * 8; // -4% 到 +4%
                    const currentPrice = config.basePrice * (1 + change24h / 100);

                    this.priceData[symbol] = {
                        price: currentPrice,
                        change24h: change24h,
                        lastUpdate: new Date()
                    };
                });
            }

            connectWebSocket() {
                try {
                    console.log('🔗 连接Coinbase WebSocket...');
                    this.websocket = new WebSocket('wss://ws-feed.exchange.coinbase.com');

                    this.websocket.onopen = () => {
                        console.log('✅ WebSocket连接成功');
                        this.isConnected = true;
                        this.subscribeToTickers();
                    };

                    this.websocket.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            if (data.type === 'ticker') {
                                this.handleTickerUpdate(data);
                            }
                        } catch (error) {
                            console.error('WebSocket消息解析错误:', error);
                        }
                    };

                    this.websocket.onerror = () => {
                        console.log('❌ WebSocket连接失败，使用模拟数据');
                        this.isConnected = false;
                    };

                    this.websocket.onclose = () => {
                        console.log('🔌 WebSocket连接关闭');
                        this.isConnected = false;
                        // 5秒后重连
                        setTimeout(() => this.connectWebSocket(), 5000);
                    };

                } catch (error) {
                    console.log('❌ WebSocket不可用，使用模拟数据');
                    this.isConnected = false;
                }
            }

            subscribeToTickers() {
                const productIds = Object.values(this.cryptoConfig).map(config => config.symbol);

                const subscribeMessage = {
                    type: 'subscribe',
                    product_ids: productIds,
                    channels: [{
                        name: 'ticker',
                        product_ids: productIds
                    }]
                };

                this.websocket.send(JSON.stringify(subscribeMessage));
                console.log('📡 已订阅价格频道:', productIds);
            }

            handleTickerUpdate(ticker) {
                const symbol = ticker.product_id.replace('-USD', '');

                if (this.cryptoConfig[symbol]) {
                    const usdPrice = parseFloat(ticker.price);
                    const open24h = parseFloat(ticker.open_24h);
                    const change24h = open24h ? ((usdPrice - open24h) / open24h) * 100 : 0;

                    this.priceData[symbol] = {
                        price: usdPrice,
                        change24h: change24h,
                        lastUpdate: new Date()
                    };

                    console.log(`💰 ${symbol}: $${usdPrice.toFixed(2)} (${change24h > 0 ? '+' : ''}${change24h.toFixed(2)}%)`);
                }
            }

            startPriceUpdates() {
                // 立即更新一次
                this.updatePriceDisplay();

                // 每3秒更新一次
                this.updateInterval = setInterval(() => {
                    if (!this.isConnected) {
                        this.simulatePriceChanges();
                    }
                    this.updatePriceDisplay();
                }, 3000);
            }

            simulatePriceChanges() {
                Object.entries(this.cryptoConfig).forEach(([symbol, config]) => {
                    if (this.priceData[symbol]) {
                        // 小幅随机波动
                        const change = (Math.random() - 0.5) * config.volatility * 2;
                        const newPrice = this.priceData[symbol].price * (1 + change);

                        // 计算24小时涨跌幅
                        const change24h = ((newPrice - config.basePrice) / config.basePrice) * 100;

                        this.priceData[symbol] = {
                            price: newPrice,
                            change24h: change24h,
                            lastUpdate: new Date()
                        };
                    }
                });
            }

            updatePriceDisplay() {
                Object.entries(this.priceData).forEach(([symbol, coinData]) => {
                    const priceItem = document.querySelector(`[data-symbol="${symbol}"]`);

                    if (priceItem && coinData) {
                        const priceElement = priceItem.querySelector('[data-price="idr"]');
                        const changeElement = priceItem.querySelector('[data-change]');

                        if (priceElement && changeElement) {
                            // 转换为IDR价格
                            const idrPrice = coinData.price * this.usdToIdrRate;
                            const formattedPrice = `IDR ${idrPrice.toLocaleString('id-ID', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            })}`;

                            // 更新价格
                            priceElement.textContent = formattedPrice;

                            // 更新涨跌幅
                            const changeSign = coinData.change24h >= 0 ? '+' : '';
                            const changeText = `${changeSign}${coinData.change24h.toFixed(2)}%`;
                            changeElement.textContent = changeText;

                            // 更新颜色
                            changeElement.className = `price-change ${coinData.change24h > 0 ? 'positive' : coinData.change24h < 0 ? 'negative' : 'neutral'}`;

                            // 添加闪烁效果
                            priceItem.classList.add('price-update');
                            priceItem.classList.add(coinData.change24h >= 0 ? 'price-flash-up' : 'price-flash-down');

                            setTimeout(() => {
                                priceItem.classList.remove('price-flash-up', 'price-flash-down');
                            }, 500);

                            console.log(`📈 更新${symbol}: ${formattedPrice} (${changeText})`);
                        }
                    }
                });
            }

            stop() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                    this.updateInterval = null;
                }
                if (this.websocket) {
                    this.websocket.close();
                    this.websocket = null;
                }
                console.log('⏹️ 价格更新系统已停止');
            }
        }

        // 初始化系统
        let priceUpdater;

        window.addEventListener('load', () => {
            setTimeout(() => {
                priceUpdater = new CoinbasePriceUpdater();
                priceUpdater.init();
            }, 1000);
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (priceUpdater) {
                priceUpdater.stop();
            }
        });
    </script>
</body>
</html>
