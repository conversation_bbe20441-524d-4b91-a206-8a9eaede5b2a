<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Crypto Prices</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: #0052ff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.3s ease;
        }
        .price-item:hover {
            background: #f8fafc;
        }
        .coin-info {
            display: flex;
            align-items: center;
        }
        .coin-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        .coin-name {
            font-weight: 600;
            color: #1f2937;
        }
        .coin-symbol {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
        }
        .price-info {
            text-align: right;
        }
        .price {
            font-weight: 600;
            color: #1f2937;
            font-size: 16px;
        }
        .change {
            font-size: 12px;
            margin-top: 2px;
        }
        .positive {
            color: #10b981;
        }
        .negative {
            color: #ef4444;
        }
        .status {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }
        .updated {
            background-color: #dcfce7 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Real-time Crypto Prices</h1>
            <p>Updates every second with live market data</p>
        </div>
        <div id="prices-container">
            <div class="status">Loading real-time prices...</div>
        </div>
    </div>

    <script>
        class CryptoPriceTracker {
            constructor() {
                this.container = document.getElementById('prices-container');
                this.coins = {
                    'bitcoin': { name: 'Bitcoin', symbol: 'BTC', color: '#f7931a' },
                    'ethereum': { name: 'Ethereum', symbol: 'ETH', color: '#627eea' },
                    'binancecoin': { name: 'BNB', symbol: 'BNB', color: '#f3ba2f' },
                    'cardano': { name: 'Cardano', symbol: 'ADA', color: '#0033ad' },
                    'solana': { name: 'Solana', symbol: 'SOL', color: '#9945ff' },
                    'polkadot': { name: 'Polkadot', symbol: 'DOT', color: '#e6007a' },
                    'dogecoin': { name: 'Dogecoin', symbol: 'DOGE', color: '#c2a633' },
                    'avalanche-2': { name: 'Avalanche', symbol: 'AVAX', color: '#e84142' },
                    'polygon': { name: 'Polygon', symbol: 'MATIC', color: '#8247e5' },
                    'chainlink': { name: 'Chainlink', symbol: 'LINK', color: '#375bd2' },
                    'litecoin': { name: 'Litecoin', symbol: 'LTC', color: '#bfbbbb' },
                    'bitcoin-cash': { name: 'Bitcoin Cash', symbol: 'BCH', color: '#8dc351' },
                    'stellar': { name: 'Stellar', symbol: 'XLM', color: '#7d00ff' },
                    'vechain': { name: 'VeChain', symbol: 'VET', color: '#15bdff' },
                    'filecoin': { name: 'Filecoin', symbol: 'FIL', color: '#0090ff' }
                };
                this.init();
            }

            async init() {
                await this.fetchAndDisplayPrices();
                setInterval(() => this.fetchAndDisplayPrices(), 1000);
            }

            async fetchAndDisplayPrices() {
                try {
                    const response = await fetch('http://localhost:3001/api/crypto');
                    const data = await response.json();
                    this.displayPrices(data);
                } catch (error) {
                    console.error('Failed to fetch prices:', error);
                    this.container.innerHTML = `
                        <div class="status">
                            ❌ Failed to connect to price server<br>
                            💡 Make sure to run: <code>node crypto-proxy-server.js</code>
                        </div>
                    `;
                }
            }

            displayPrices(data) {
                let html = '';
                
                Object.entries(data).forEach(([coinId, priceData]) => {
                    const coin = this.coins[coinId];
                    if (!coin) return;

                    const price = priceData.usd;
                    const change = priceData.usd_24h_change || 0;
                    const isPositive = change >= 0;
                    
                    const formattedPrice = price >= 1 
                        ? `$${price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`
                        : `$${price.toFixed(6)}`;
                    
                    html += `
                        <div class="price-item" id="coin-${coinId}">
                            <div class="coin-info">
                                <div class="coin-icon" style="background: ${coin.color}">
                                    ${coin.symbol.substring(0, 3)}
                                </div>
                                <div>
                                    <div class="coin-name">${coin.name}</div>
                                    <div class="coin-symbol">${coin.symbol}</div>
                                </div>
                            </div>
                            <div class="price-info">
                                <div class="price">${formattedPrice}</div>
                                <div class="change ${isPositive ? 'positive' : 'negative'}">
                                    ${isPositive ? '+' : ''}${change.toFixed(2)}%
                                </div>
                            </div>
                        </div>
                    `;
                });

                this.container.innerHTML = html;
                
                // Add flash effect to show update
                document.querySelectorAll('.price-item').forEach(item => {
                    item.classList.add('updated');
                    setTimeout(() => item.classList.remove('updated'), 300);
                });
            }
        }

        // Start the price tracker
        new CryptoPriceTracker();
    </script>
</body>
</html>
