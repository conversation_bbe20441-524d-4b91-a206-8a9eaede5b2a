<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coinbase 价格监控</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #1a1a1a;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 16px;
        }
        .crypto-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .crypto-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .crypto-card:hover {
            transform: translateY(-2px);
        }
        .crypto-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .crypto-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #666;
        }
        .crypto-name {
            flex: 1;
        }
        .crypto-name h3 {
            margin: 0;
            font-size: 18px;
            color: #1a1a1a;
        }
        .crypto-name p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
        .crypto-price {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .crypto-change {
            font-size: 14px;
            font-weight: 500;
        }
        .positive { color: #16a34a; }
        .negative { color: #dc2626; }
        .status-bar {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-item {
            display: inline-block;
            margin-right: 20px;
            font-size: 14px;
        }
        .status-label {
            color: #666;
            margin-right: 5px;
        }
        .status-value {
            font-weight: 500;
            color: #1a1a1a;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Coinbase 实时价格监控</h1>
            <p>实时显示主要加密货币价格和涨跌幅</p>
        </div>

        <div class="status-bar">
            <span class="status-item">
                <span class="status-label">连接状态:</span>
                <span class="status-value" id="connection-status">连接中...</span>
            </span>
            <span class="status-item">
                <span class="status-label">更新时间:</span>
                <span class="status-value" id="last-update">--</span>
            </span>
            <span class="status-item">
                <span class="status-label">币种数量:</span>
                <span class="status-value" id="coin-count">0</span>
            </span>
        </div>

        <div class="crypto-grid" id="crypto-grid">
            <div class="loading">正在加载价格数据...</div>
        </div>
    </div>

    <script>
        // 简化的价格更新系统
        class CryptoPriceMonitor {
            constructor() {
                this.priceData = {};
                this.updateInterval = null;
                
                // 模拟价格数据
                this.cryptoData = {
                    'BTC': { 
                        name: 'Bitcoin', 
                        symbol: 'BTC',
                        basePrice: 43250, 
                        volatility: 0.02,
                        icon: '₿'
                    },
                    'ETH': { 
                        name: 'Ethereum', 
                        symbol: 'ETH',
                        basePrice: 2580, 
                        volatility: 0.03,
                        icon: 'Ξ'
                    },
                    'ADA': { 
                        name: 'Cardano', 
                        symbol: 'ADA',
                        basePrice: 0.485, 
                        volatility: 0.05,
                        icon: '₳'
                    },
                    'SOL': { 
                        name: 'Solana', 
                        symbol: 'SOL',
                        basePrice: 98.75, 
                        volatility: 0.04,
                        icon: '◎'
                    },
                    'DOT': { 
                        name: 'Polkadot', 
                        symbol: 'DOT',
                        basePrice: 7.23, 
                        volatility: 0.03,
                        icon: '●'
                    },
                    'ATOM': { 
                        name: 'Cosmos', 
                        symbol: 'ATOM',
                        basePrice: 16.41, 
                        volatility: 0.04,
                        icon: '⚛'
                    }
                };
            }

            init() {
                console.log('🚀 初始化加密货币价格监控系统...');
                this.generateInitialPrices();
                this.renderCryptoCards();
                this.startPriceUpdates();
                this.updateStatus();
            }

            generateInitialPrices() {
                Object.entries(this.cryptoData).forEach(([symbol, config]) => {
                    const change24h = (Math.random() - 0.5) * 10; // -5% 到 +5%
                    const currentPrice = config.basePrice * (1 + change24h / 100);
                    
                    this.priceData[symbol] = {
                        price: currentPrice,
                        change24h: change24h,
                        lastUpdate: new Date()
                    };
                });
            }

            renderCryptoCards() {
                const grid = document.getElementById('crypto-grid');
                grid.innerHTML = '';

                Object.entries(this.cryptoData).forEach(([symbol, config]) => {
                    const data = this.priceData[symbol];
                    const card = this.createCryptoCard(symbol, config, data);
                    grid.appendChild(card);
                });
            }

            createCryptoCard(symbol, config, data) {
                const card = document.createElement('div');
                card.className = 'crypto-card';
                card.id = `card-${symbol}`;

                const changeClass = data.change24h >= 0 ? 'positive' : 'negative';
                const changeSign = data.change24h >= 0 ? '+' : '';

                card.innerHTML = `
                    <div class="crypto-header">
                        <div class="crypto-icon">${config.icon}</div>
                        <div class="crypto-name">
                            <h3>${config.name}</h3>
                            <p>${config.symbol}</p>
                        </div>
                    </div>
                    <div class="crypto-price" id="price-${symbol}">
                        ${this.formatPrice(data.price)}
                    </div>
                    <div class="crypto-change ${changeClass}" id="change-${symbol}">
                        ${changeSign}${data.change24h.toFixed(2)}%
                    </div>
                `;

                return card;
            }

            startPriceUpdates() {
                console.log('⚡ 启动实时价格更新...');
                document.getElementById('connection-status').textContent = '✅ 已连接';
                
                this.updateInterval = setInterval(() => {
                    this.updatePrices();
                    this.updateDisplay();
                    this.updateStatus();
                }, 2000); // 每2秒更新
            }

            updatePrices() {
                Object.entries(this.cryptoData).forEach(([symbol, config]) => {
                    if (this.priceData[symbol]) {
                        // 小幅随机波动
                        const volatility = config.volatility;
                        const change = (Math.random() - 0.5) * volatility * 2;
                        const newPrice = this.priceData[symbol].price * (1 + change);
                        
                        // 计算24小时涨跌幅
                        const change24h = ((newPrice - config.basePrice) / config.basePrice) * 100;
                        
                        this.priceData[symbol] = {
                            price: newPrice,
                            change24h: change24h,
                            lastUpdate: new Date()
                        };
                    }
                });
            }

            updateDisplay() {
                Object.entries(this.priceData).forEach(([symbol, data]) => {
                    const priceElement = document.getElementById(`price-${symbol}`);
                    const changeElement = document.getElementById(`change-${symbol}`);
                    
                    if (priceElement && changeElement) {
                        // 更新价格
                        priceElement.textContent = this.formatPrice(data.price);
                        
                        // 更新涨跌幅
                        const changeSign = data.change24h >= 0 ? '+' : '';
                        changeElement.textContent = `${changeSign}${data.change24h.toFixed(2)}%`;
                        
                        // 更新颜色
                        changeElement.className = `crypto-change ${data.change24h >= 0 ? 'positive' : 'negative'}`;
                        
                        // 添加更新动画
                        const card = document.getElementById(`card-${symbol}`);
                        card.style.transform = 'scale(1.02)';
                        setTimeout(() => {
                            card.style.transform = 'scale(1)';
                        }, 200);
                    }
                });
            }

            updateStatus() {
                document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
                document.getElementById('coin-count').textContent = Object.keys(this.priceData).length;
            }

            formatPrice(price) {
                if (price >= 1000) {
                    return `$${price.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    })}`;
                } else if (price >= 1) {
                    return `$${price.toFixed(4)}`;
                } else {
                    return `$${price.toFixed(6)}`;
                }
            }

            stop() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                    this.updateInterval = null;
                    document.getElementById('connection-status').textContent = '❌ 已断开';
                    console.log('⏹️ 价格更新已停止');
                }
            }
        }

        // 初始化系统
        let priceMonitor;
        
        window.addEventListener('load', () => {
            priceMonitor = new CryptoPriceMonitor();
            priceMonitor.init();
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (priceMonitor) {
                priceMonitor.stop();
            }
        });
    </script>
</body>
</html>
