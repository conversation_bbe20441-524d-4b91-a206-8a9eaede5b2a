const http = require('http');
const https = require('https');
const url = require('url');

const PORT = 3001;

// Cache for price data
let priceCache = null;
let lastFetchTime = 0;
const CACHE_DURATION = 5000; // 5 seconds cache

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Content-Type': 'application/json'
};

const server = http.createServer((req, res) => {
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200, corsHeaders);
    res.end();
    return;
  }

  // Only handle GET requests to /api/crypto
  if (req.method === 'GET' && req.url.startsWith('/api/crypto')) {
    const now = Date.now();

    // Return cached data if available and fresh
    if (priceCache && (now - lastFetchTime) < CACHE_DURATION) {
      console.log('📦 Returning cached data');
      res.writeHead(200, corsHeaders);
      res.end(JSON.stringify(priceCache));
      return;
    }

    const coins = [
      'bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana',
      'polkadot', 'dogecoin', 'avalanche-2', 'polygon', 'chainlink',
      'litecoin', 'bitcoin-cash', 'stellar', 'vechain', 'filecoin'
    ];

    const apiUrl = `https://api.coingecko.com/api/v3/simple/price?ids=${coins.join(',')}&vs_currencies=usd&include_24hr_change=true`;

    console.log('🔄 Fetching fresh crypto prices from CoinGecko...');

    const request = https.get(apiUrl, (apiRes) => {
      let data = '';

      apiRes.on('data', (chunk) => {
        data += chunk;
      });

      apiRes.on('end', () => {
        try {
          const parsedData = JSON.parse(data);
          priceCache = parsedData;
          lastFetchTime = now;

          res.writeHead(200, corsHeaders);
          res.end(data);
          console.log('✅ Fresh crypto prices sent successfully');
        } catch (parseError) {
          console.error('❌ Error parsing response:', parseError.message);
          res.writeHead(500, corsHeaders);
          res.end(JSON.stringify({ error: 'Invalid response from CoinGecko' }));
        }
      });

    });

    request.on('error', (err) => {
      console.error('❌ Error fetching from CoinGecko:', err.message);

      // Return cached data if available, even if stale
      if (priceCache) {
        console.log('📦 Returning stale cached data due to error');
        res.writeHead(200, corsHeaders);
        res.end(JSON.stringify(priceCache));
      } else {
        res.writeHead(500, corsHeaders);
        res.end(JSON.stringify({ error: 'Failed to fetch crypto prices and no cache available' }));
      }
    });

    request.setTimeout(10000, () => {
      console.error('❌ Request timeout');
      request.destroy();

      if (priceCache) {
        console.log('📦 Returning cached data due to timeout');
        res.writeHead(200, corsHeaders);
        res.end(JSON.stringify(priceCache));
      } else {
        res.writeHead(500, corsHeaders);
        res.end(JSON.stringify({ error: 'Request timeout and no cache available' }));
      }
    });

  } else {
    res.writeHead(404, corsHeaders);
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

server.listen(PORT, () => {
  console.log(`🚀 Crypto Proxy Server running on http://localhost:${PORT}`);
  console.log(`📊 API endpoint: http://localhost:${PORT}/api/crypto`);
  console.log('💡 This server will fetch real-time crypto prices from CoinGecko API');
});
