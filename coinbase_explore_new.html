<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore · Coinbase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #ffffff;
            color: #1a1a1a;
            line-height: 1.5;
        }

        .container {
            max-width: 412px;
            margin: 0 auto;
            background: #ffffff;
            min-height: 100vh;
            font-size: 14px;
        }

        .header {
            padding: 12px 16px;
            background: #ffffff;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 500;
            color: #1a1a1a;
        }

        .section {
            padding: 16px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #1a1a1a;
        }

        .earning-assets {
            margin-bottom: 32px;
        }

        .earning-subtitle {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 16px;
        }

        .asset-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .asset-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
        }

        .asset-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .asset-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            color: white;
        }

        .asset-details h3 {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .asset-details p {
            font-size: 14px;
            color: #6b7280;
        }

        .asset-apy {
            font-size: 14px;
            color: #16a34a;
            font-weight: 500;
        }

        .prices-section {
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }

        .price-item {
            padding: 8px 0;
            font-size: 14px;
            line-height: 1.4;
        }

        .price-line {
            display: flex;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
        }

        .coin-name {
            font-weight: 500;
            color: #1a1a1a;
        }

        .coin-symbol {
            color: #6b7280;
        }

        .coin-apy {
            color: #6b7280;
        }

        .price-value {
            font-weight: 500;
            color: #1a1a1a;
        }

        .price-change {
            font-weight: 500;
        }

        .positive { color: #16a34a; }
        .negative { color: #dc2626; }
        .neutral { color: #6b7280; }

        .see-all {
            text-align: center;
            padding: 16px 0;
            color: #6b7280;
            font-size: 14px;
            cursor: pointer;
        }

        /* 币种图标颜色 */
        .btc { background: #f7931a; }
        .eth { background: #627eea; }
        .xrp { background: #23292f; }
        .usdt { background: #26a17b; }
        .bnb { background: #f3ba2f; }
        .atom { background: #2e3148; }
        .dot { background: #e6007a; }
        .sol { background: #9945ff; }
        .avax { background: #e84142; }
        .xtz { background: #2c7df7; }

        /* 价格更新动画 */
        .price-update {
            transition: all 0.3s ease;
        }

        .price-flash-up {
            background-color: #dcfce7 !important;
        }

        .price-flash-down {
            background-color: #fef2f2 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Explore</h1>
        </div>

        <div class="section">
            <!-- Earning Assets Section -->
            <div class="earning-assets">
                <h2 class="section-title">Earning assets</h2>
                <p class="earning-subtitle">Earn rewards by buying and staking eligible assets</p>
                
                <div class="asset-list">
                    <div class="asset-item">
                        <div class="asset-info">
                            <div class="asset-icon atom">⚛</div>
                            <div class="asset-details">
                                <h3>Cosmos</h3>
                                <p>ATOM</p>
                            </div>
                        </div>
                        <div class="asset-apy">+0.30% APY</div>
                    </div>

                    <div class="asset-item">
                        <div class="asset-info">
                            <div class="asset-icon dot">●</div>
                            <div class="asset-details">
                                <h3>Polkadot</h3>
                                <p>DOT</p>
                            </div>
                        </div>
                        <div class="asset-apy">+0.30% APY</div>
                    </div>

                    <div class="asset-item">
                        <div class="asset-info">
                            <div class="asset-icon sol">◆</div>
                            <div class="asset-details">
                                <h3>Solana</h3>
                                <p>SOL</p>
                            </div>
                        </div>
                        <div class="asset-apy">+0.30% APY</div>
                    </div>

                    <div class="asset-item">
                        <div class="asset-info">
                            <div class="asset-icon avax">▲</div>
                            <div class="asset-details">
                                <h3>Avalanche</h3>
                                <p>AVAX</p>
                            </div>
                        </div>
                        <div class="asset-apy">+0.30% APY</div>
                    </div>

                    <div class="asset-item">
                        <div class="asset-info">
                            <div class="asset-icon xtz">◉</div>
                            <div class="asset-details">
                                <h3>Tezos</h3>
                                <p>XTZ</p>
                            </div>
                        </div>
                        <div class="asset-apy">+0.30% APY</div>
                    </div>
                </div>

                <div class="see-all">See all</div>
            </div>

            <!-- Prices Section -->
            <div class="prices-section">
                <h2 class="section-title">Prices</h2>
                
                <div class="price-item" data-symbol="BTC">
                    <div class="price-info">
                        <div class="price-icon btc">₿</div>
                        <div class="price-details">
                            <h3>Bitcoin</h3>
                            <p>BTC</p>
                        </div>
                    </div>
                    <div class="price-data">
                        <div class="price-value" data-price="idr">IDR 660,034,517.70</div>
                        <div class="price-change negative" data-change>-0.26%</div>
                    </div>
                </div>

                <div class="price-item" data-symbol="ETH">
                    <div class="price-info">
                        <div class="price-icon eth">Ξ</div>
                        <div class="price-details">
                            <h3>Ethereum</h3>
                            <p>ETH · 1.93% APY</p>
                        </div>
                    </div>
                    <div class="price-data">
                        <div class="price-value" data-price="idr">IDR 39,474,328.53</div>
                        <div class="price-change negative" data-change>-2.33%</div>
                    </div>
                </div>

                <div class="price-item" data-symbol="XRP">
                    <div class="price-info">
                        <div class="price-icon xrp">◉</div>
                        <div class="price-details">
                            <h3>XRP</h3>
                            <p>XRP</p>
                        </div>
                    </div>
                    <div class="price-data">
                        <div class="price-value" data-price="idr">IDR 10,710.28</div>
                        <div class="price-change negative" data-change>-4.01%</div>
                    </div>
                </div>

                <div class="price-item" data-symbol="USDT">
                    <div class="price-info">
                        <div class="price-icon usdt">₮</div>
                        <div class="price-details">
                            <h3>Tether</h3>
                            <p>USDT</p>
                        </div>
                    </div>
                    <div class="price-data">
                        <div class="price-value" data-price="idr">IDR 15,300.00</div>
                        <div class="price-change neutral" data-change>-0.00%</div>
                    </div>
                </div>

                <div class="price-item" data-symbol="BNB">
                    <div class="price-info">
                        <div class="price-icon bnb">◆</div>
                        <div class="price-details">
                            <h3>BNB</h3>
                            <p>BNB</p>
                        </div>
                    </div>
                    <div class="price-data">
                        <div class="price-value" data-price="idr">IDR 9,256,500.00</div>
                        <div class="price-change negative" data-change>-2.16%</div>
                    </div>
                </div>

                <div class="see-all">See all</div>
            </div>
        </div>
    </div>

    <script>
        // ===== Coinbase实时价格更新系统 =====
        class CoinbasePriceUpdater {
            constructor() {
                this.websocket = null;
                this.isConnected = false;
                this.priceData = {};
                this.updateInterval = null;

                // 币种配置
                this.cryptoConfig = {
                    'BTC': { name: 'Bitcoin', symbol: 'BTC-USD', basePrice: 43250, volatility: 0.02 },
                    'ETH': { name: 'Ethereum', symbol: 'ETH-USD', basePrice: 2580, volatility: 0.03 },
                    'XRP': { name: 'XRP', symbol: 'XRP-USD', basePrice: 0.70, volatility: 0.05 },
                    'USDT': { name: 'Tether', symbol: 'USDT-USD', basePrice: 1.00, volatility: 0.001 },
                    'BNB': { name: 'BNB', symbol: 'BNB-USD', basePrice: 605, volatility: 0.03 }
                };

                this.usdToIdrRate = 15300;
            }

            init() {
                console.log('🚀 启动Coinbase实时价格更新系统...');
                this.generateInitialPrices();
                this.connectWebSocket();
                this.startPriceUpdates();
            }

            generateInitialPrices() {
                Object.entries(this.cryptoConfig).forEach(([symbol, config]) => {
                    const change24h = (Math.random() - 0.5) * 8; // -4% 到 +4%
                    const currentPrice = config.basePrice * (1 + change24h / 100);

                    this.priceData[symbol] = {
                        price: currentPrice,
                        change24h: change24h,
                        lastUpdate: new Date()
                    };
                });
            }

            connectWebSocket() {
                try {
                    console.log('🔗 连接Coinbase WebSocket...');
                    this.websocket = new WebSocket('wss://ws-feed.exchange.coinbase.com');

                    this.websocket.onopen = () => {
                        console.log('✅ WebSocket连接成功');
                        this.isConnected = true;
                        this.subscribeToTickers();
                    };

                    this.websocket.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            if (data.type === 'ticker') {
                                this.handleTickerUpdate(data);
                            }
                        } catch (error) {
                            console.error('WebSocket消息解析错误:', error);
                        }
                    };

                    this.websocket.onerror = () => {
                        console.log('❌ WebSocket连接失败，使用模拟数据');
                        this.isConnected = false;
                    };

                    this.websocket.onclose = () => {
                        console.log('🔌 WebSocket连接关闭');
                        this.isConnected = false;
                        // 5秒后重连
                        setTimeout(() => this.connectWebSocket(), 5000);
                    };

                } catch (error) {
                    console.log('❌ WebSocket不可用，使用模拟数据');
                    this.isConnected = false;
                }
            }

            subscribeToTickers() {
                const productIds = Object.values(this.cryptoConfig).map(config => config.symbol);

                const subscribeMessage = {
                    type: 'subscribe',
                    product_ids: productIds,
                    channels: [{
                        name: 'ticker',
                        product_ids: productIds
                    }]
                };

                this.websocket.send(JSON.stringify(subscribeMessage));
                console.log('📡 已订阅价格频道:', productIds);
            }

            handleTickerUpdate(ticker) {
                const symbol = ticker.product_id.replace('-USD', '');

                if (this.cryptoConfig[symbol]) {
                    const usdPrice = parseFloat(ticker.price);
                    const open24h = parseFloat(ticker.open_24h);
                    const change24h = open24h ? ((usdPrice - open24h) / open24h) * 100 : 0;

                    this.priceData[symbol] = {
                        price: usdPrice,
                        change24h: change24h,
                        lastUpdate: new Date()
                    };

                    console.log(`💰 ${symbol}: $${usdPrice.toFixed(2)} (${change24h > 0 ? '+' : ''}${change24h.toFixed(2)}%)`);
                }
            }

            startPriceUpdates() {
                // 立即更新一次
                this.updatePriceDisplay();

                // 每3秒更新一次
                this.updateInterval = setInterval(() => {
                    if (!this.isConnected) {
                        this.simulatePriceChanges();
                    }
                    this.updatePriceDisplay();
                }, 3000);
            }

            simulatePriceChanges() {
                Object.entries(this.cryptoConfig).forEach(([symbol, config]) => {
                    if (this.priceData[symbol]) {
                        // 小幅随机波动
                        const change = (Math.random() - 0.5) * config.volatility * 2;
                        const newPrice = this.priceData[symbol].price * (1 + change);

                        // 计算24小时涨跌幅
                        const change24h = ((newPrice - config.basePrice) / config.basePrice) * 100;

                        this.priceData[symbol] = {
                            price: newPrice,
                            change24h: change24h,
                            lastUpdate: new Date()
                        };
                    }
                });
            }

            updatePriceDisplay() {
                Object.entries(this.priceData).forEach(([symbol, coinData]) => {
                    const priceItem = document.querySelector(`[data-symbol="${symbol}"]`);

                    if (priceItem && coinData) {
                        const priceElement = priceItem.querySelector('[data-price="idr"]');
                        const changeElement = priceItem.querySelector('[data-change]');

                        if (priceElement && changeElement) {
                            // 转换为IDR价格
                            const idrPrice = coinData.price * this.usdToIdrRate;
                            const formattedPrice = `IDR ${idrPrice.toLocaleString('id-ID', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            })}`;

                            // 更新价格
                            priceElement.textContent = formattedPrice;

                            // 更新涨跌幅
                            const changeSign = coinData.change24h >= 0 ? '+' : '';
                            const changeText = `${changeSign}${coinData.change24h.toFixed(2)}%`;
                            changeElement.textContent = changeText;

                            // 更新颜色
                            changeElement.className = `price-change ${coinData.change24h > 0 ? 'positive' : coinData.change24h < 0 ? 'negative' : 'neutral'}`;

                            // 添加闪烁效果
                            priceItem.classList.add('price-update');
                            priceItem.classList.add(coinData.change24h >= 0 ? 'price-flash-up' : 'price-flash-down');

                            setTimeout(() => {
                                priceItem.classList.remove('price-flash-up', 'price-flash-down');
                            }, 500);

                            console.log(`📈 更新${symbol}: ${formattedPrice} (${changeText})`);
                        }
                    }
                });
            }

            stop() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                    this.updateInterval = null;
                }
                if (this.websocket) {
                    this.websocket.close();
                    this.websocket = null;
                }
                console.log('⏹️ 价格更新系统已停止');
            }
        }

        // 初始化系统
        let priceUpdater;

        window.addEventListener('load', () => {
            setTimeout(() => {
                priceUpdater = new CoinbasePriceUpdater();
                priceUpdater.init();
            }, 1000);
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (priceUpdater) {
                priceUpdater.stop();
            }
        });
    </script>
</body>
</html>
