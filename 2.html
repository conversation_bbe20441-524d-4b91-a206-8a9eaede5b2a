<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>信息收集 - 页面2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #ffa726);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 18px;
            opacity: 0.9;
        }

        .form-content {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 30px;
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 20px 0 10px 0;
            border: none;
            border-bottom: 2px solid #e0e0e0;
            background: transparent;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-bottom-color: #ff6b6b;
        }

        .form-group label {
            position: absolute;
            top: 20px;
            left: 0;
            font-size: 18px;
            color: #999;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .form-group input:focus + label,
        .form-group input:not(:placeholder-shown) + label {
            top: 0;
            font-size: 14px;
            color: #ff6b6b;
            font-weight: 600;
        }

        .form-group::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(45deg, #ff6b6b, #ffa726);
            transition: width 0.3s ease;
        }

        .form-group input:focus ~ ::after {
            width: 100%;
        }

        .button-group {
            display: flex;
            gap: 20px;
            margin-top: 50px;
        }

        .btn {
            flex: 1;
            padding: 18px 30px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #ff6b6b, #ffa726);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: #666;
            border: 2px solid #ddd;
        }

        .btn-secondary:hover {
            background: #f8f9fa;
            border-color: #ff6b6b;
            color: #ff6b6b;
            transform: translateY(-2px);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 28px;
            }

            .form-content {
                padding: 30px 20px;
            }

            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>数据采集中心</h1>
            <p>安全 · 快速 · 可靠</p>
        </div>

        <div class="form-content">
            <form id="dataForm">
                <div class="form-group">
                    <input type="text" id="input1" name="input1" placeholder=" " required>
                    <label for="input1">第一项信息</label>
                </div>

                <div class="form-group">
                    <input type="text" id="input2" name="input2" placeholder=" " required>
                    <label for="input2">第二项信息</label>
                </div>

                <div class="form-group">
                    <input type="text" id="input3" name="input3" placeholder=" " required>
                    <label for="input3">第三项信息</label>
                </div>

                <div class="button-group">
                    <button type="submit" class="btn btn-primary">立即开始</button>
                    <button type="button" class="btn btn-secondary" onclick="handleReturn()">返回</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('dataForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const input1 = document.getElementById('input1').value;
            const input2 = document.getElementById('input2').value;
            const input3 = document.getElementById('input3').value;
            
            if (!input1 || !input2 || !input3) {
                showNotification('请完整填写所有信息', 'error');
                return;
            }
            
            showNotification('数据收集成功！正在处理...', 'success');
            
            setTimeout(() => {
                alert('处理完成！\n信息一: ' + input1 + '\n信息二: ' + input2 + '\n信息三: ' + input3);
            }, 1500);
        });

        function handleReturn() {
            if (confirm('确认返回？未保存的数据将丢失。')) {
                window.history.back();
            }
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 1000;
                animation: slideInRight 0.3s ease;
                ${type === 'success' ? 'background: #4caf50;' : 'background: #f44336;'}
            `;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 添加输入动画
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateX(10px)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateX(0)';
            });
        });
    </script>
</body>
</html>
