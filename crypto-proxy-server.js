const http = require('http');
const https = require('https');
const url = require('url');

const PORT = 3001;

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Content-Type': 'application/json'
};

const server = http.createServer((req, res) => {
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200, corsHeaders);
    res.end();
    return;
  }

  // Only handle GET requests to /api/crypto
  if (req.method === 'GET' && req.url.startsWith('/api/crypto')) {
    const coins = [
      'bitcoin', 'ethereum', 'binancecoin', 'cardano', 'solana', 
      'polkadot', 'dogecoin', 'avalanche-2', 'polygon', 'chainlink',
      'litecoin', 'bitcoin-cash', 'stellar', 'vechain', 'filecoin'
    ];

    const apiUrl = `https://api.coingecko.com/api/v3/simple/price?ids=${coins.join(',')}&vs_currencies=usd&include_24hr_change=true`;

    console.log('Fetching crypto prices from CoinGecko...');

    https.get(apiUrl, (apiRes) => {
      let data = '';

      apiRes.on('data', (chunk) => {
        data += chunk;
      });

      apiRes.on('end', () => {
        res.writeHead(200, corsHeaders);
        res.end(data);
        console.log('✅ Crypto prices sent successfully');
      });

    }).on('error', (err) => {
      console.error('❌ Error fetching from CoinGecko:', err.message);
      res.writeHead(500, corsHeaders);
      res.end(JSON.stringify({ error: 'Failed to fetch crypto prices' }));
    });

  } else {
    res.writeHead(404, corsHeaders);
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

server.listen(PORT, () => {
  console.log(`🚀 Crypto Proxy Server running on http://localhost:${PORT}`);
  console.log(`📊 API endpoint: http://localhost:${PORT}/api/crypto`);
  console.log('💡 This server will fetch real-time crypto prices from CoinGecko API');
});
